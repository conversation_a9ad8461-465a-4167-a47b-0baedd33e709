import React from 'react';

const Card = ({ title, value, icon, description }) => {
  return (
    <div className="bg-white p-6 rounded-xl shadow-md">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm text-gray-500 font-medium">{title}</h3>
        {icon && <span className="text-gray-400">{icon}</span>}
      </div>
      <div className="text-2xl font-bold">{value}</div>
      {description && <p className="text-sm text-gray-400 mt-1">{description}</p>}
    </div>
  );
};

export default Card;
