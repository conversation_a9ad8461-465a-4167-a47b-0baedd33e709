
import React from 'react';
import Header from './Header';
import MovesManagement from './MovesManagement';
import FundraisingOverview from './FundraisingOverview';
import CrimsonInsights from './CrimsonInsights';
import TaskList from './TaskList';

const Dashboard = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Fundraiser Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what needs your attention today.</p>
        </div>
        
        <div className="grid grid-cols-1 gap-6 mb-6">
          <FundraisingOverview />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <MovesManagement />
          <TaskList />
        </div>
        
        <div className="grid grid-cols-1 gap-6">
          <CrimsonInsights />
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
