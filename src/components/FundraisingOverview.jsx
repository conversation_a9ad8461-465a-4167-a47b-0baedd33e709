import React, { useState } from 'react';
import TabSwitcher from './TabSwitcher';
import FundraisingTabs from './FundraisingTabs';

function FundraisingOverview() {
  // Define tab labels for the three categories
  const tabs = ['Events This Week', 'Pledges Due', 'Past Due'];

  // State to track which tab is active (0 = first tab)
  const [activeTab, setActiveTab] = useState(0);

  // State to handle collapse of this panel
  const [collapsed, setCollapsed] = useState(false);

  return (
    <div className="bg-white dark:bg-[#2C4251] shadow rounded-lg p-4">
      {/* Panel header with title and collapse button */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Fundraising Overview</h2>
        <button 
          onClick={() => setCollapsed(!collapsed)} 
          className="text-xl font-semibold focus:outline-none"
          aria-label={collapsed ? "Expand section" : "Collapse section"}
        >
          {collapsed ? '+' : '–'}
        </button>
      </div>

      {/* Tabbed content (only rendered when not collapsed) */}
      {!collapsed && (
        <div className="mt-3">
          {/* Tab switcher navigation */}
          <TabSwitcher 
            tabs={tabs} 
            activeIndex={activeTab} 
            onTabSelect={(index) => setActiveTab(index)} 
          />
          {/* Content for the active tab */}
          <FundraisingTabs activeIndex={activeTab} />
        </div>
      )}
    </div>
  );
}

export default FundraisingOverview;
