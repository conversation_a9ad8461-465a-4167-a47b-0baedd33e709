import React from 'react';

function FundraisingTabs({ activeIndex }) {
  // Static data for each tab's content
  const eventsThisWeek = [
    { id: 1, name: 'Fall Gala', date: 'Sep 15, 2025' },
    { id: 2, name: 'Donor Breakfast', date: 'Sep 17, 2025' },
    { id: 3, name: 'Volunteer Meetup', date: 'Sep 18, 2025' }
  ];
  const pledgesDue = [
    { id: 1, name: '<PERSON>', amount: '$1,000', dueDate: 'Sep 20, 2025' },
    { id: 2, name: 'ACME Corp', amount: '$5,000', dueDate: 'Oct 01, 2025' },
    { id: 3, name: '<PERSON>', amount: '$250', dueDate: 'Sep 25, 2025' }
  ];
  const pastDue = [
    { id: 1, name: '<PERSON>', amount: '$300', dueDate: 'Sep 01, 2025' },
    { id: 2, name: 'XYZ PAC', amount: '$1,000', dueDate: 'Aug 20, 2025' },
    { id: 3, name: '<PERSON>', amount: '$50', dueDate: 'Sep 10, 2025' }
  ];

  return (
    <div className="mt-3">
      {activeIndex === 0 && (
        <ul className="space-y-1">
          {eventsThisWeek.map(event => (
            <li key={event.id}>
              {event.name} – <span className="text-sm text-gray-700 dark:text-gray-300">{event.date}</span>
            </li>
          ))}
        </ul>
      )}
      {activeIndex === 1 && (
        <ul className="space-y-1">
          {pledgesDue.map(item => (
            <li key={item.id}>
              {item.name}: <span className="text-sm text-gray-800 dark:text-gray-300">{item.amount}</span> due by <span className="text-sm text-gray-600 dark:text-gray-400">{item.dueDate}</span>
            </li>
          ))}
        </ul>
      )}
      {activeIndex === 2 && (
        <ul className="space-y-1">
          {pastDue.map(item => (
            <li key={item.id}>
              {item.name}: <span className="text-sm text-gray-800 dark:text-gray-300">{item.amount}</span> (was due <span className="text-sm text-gray-600 dark:text-gray-400">{item.dueDate}</span>)
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default FundraisingTabs;
