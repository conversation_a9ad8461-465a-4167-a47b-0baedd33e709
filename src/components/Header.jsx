import React from 'react';

function Header({ darkMode, toggleDarkMode }) {
  return (
    // Header container with brand background color and padding
    <header className="bg-[#2C4251] text-white p-4 flex items-center justify-between">
      {/* Logo / Brand name */}
      <h1 className="text-xl font-bold">
        CMDI Signals
      </h1>
      {/* Theme toggle button: switches between light and dark modes */}
      <button 
        onClick={toggleDarkMode} 
        className="bg-[#D3F3EE] text-[#2C4251] font-medium px-3 py-1 rounded-full hover:bg-[#7FB7BE] hover:text-white transition-colors duration-300"
        aria-label="Toggle theme"
      >
        {/* Show moon icon & 'Dark' text when in light mode, and sun icon & 'Light' text when in dark mode */}
        {darkMode ? '☀️ Light Mode' : '🌙 Dark Mode'}
      </button>
    </header>
  );
}

export default Header;
