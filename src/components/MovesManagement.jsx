import React, { useState } from 'react';

function MovesManagement() {
  // Example static data: top 5 donor follow-up items with status badges
  const followUps = [
    { id: 1, name: '<PERSON>', action: 'Follow up on pledge payment', status: 'Overdue' },
    { id: 2, name: '<PERSON>', action: 'Send event invitation', status: 'Scheduled' },
    { id: 3, name: '<PERSON>', action: 'Arrange meeting for next week', status: 'In Progress' },
    { id: 4, name: '<PERSON>', action: 'Call to thank for last donation', status: 'Pending' },
    { id: 5, name: '<PERSON>', action: 'Personalize holiday card', status: 'Pending' }
  ];

  // State to handle collapse/expand of this panel (expanded by default)
  const [collapsed, setCollapsed] = useState(false);

  return (
    <div className="bg-white dark:bg-[#2C4251] shadow rounded-lg p-4">
      {/* Panel header with title and collapse toggle button */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Moves Management</h2>
        {/* Collapse/expand button */}
        <button 
          onClick={() => setCollapsed(!collapsed)} 
          className="text-xl font-semibold focus:outline-none"
          aria-label={collapsed ? "Expand section" : "Collapse section"}
        >
          {/* Show + if collapsed, - if expanded */}
          {collapsed ? '+' : '–'}
        </button>
      </div>

      {/* Follow-up list content (visible only when not collapsed) */}
      {!collapsed && (
        <div className="mt-3 space-y-2">
          {followUps.map(item => (
            <div key={item.id} className="flex items-center justify-between">
              {/* Donor name and action description */}
              <div>
                <strong>{item.name}</strong>
                <span className="text-sm text-gray-600 dark:text-gray-300"> – {item.action}</span>
              </div>
              {/* Status badge with color based on status */}
              <span className={`px-2 py-0.5 rounded-full text-xs font-semibold
                ${item.status === 'Overdue' ? 'bg-[#BC2C1A] text-white' 
                  : item.status === 'In Progress' ? 'bg-[#2C4251] text-white' 
                  : item.status === 'Scheduled' ? 'bg-[#7FB7BE] text-white' 
                  : 'bg-[#D3F3EE] text-[#252627]'
                }`}>
                {item.status}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default MovesManagement;
