import React from 'react';

function TabSwitcher({ tabs, activeIndex, onTabSelect }) {
  return (
    <div className="flex space-x-2 border-b border-gray-200 dark:border-gray-700 pb-1">
      {tabs.map((tabLabel, index) => (
        <button
          key={index}
          onClick={() => onTabSelect(index)}
          className={`px-3 py-1 rounded-t-md font-medium focus:outline-none 
            ${activeIndex === index 
              ? 'bg-[#7FB7BE] text-white'  // Active tab: primary color background, white text
              : 'bg-[#D3F3EE] text-[#252627] hover:ring-2 hover:ring-[#7FB7BE]'}  // Inactive: accent background, dark text, highlight on hover
          `}
        >
          {tabLabel}
        </button>
      ))}
    </div>
  );
}

export default TabSwitcher;
