import React from 'react';

const TaskList = () => {
  const tasks = [
    { id: 1, title: 'Call <PERSON> about annual pledge', dueDate: '2023-10-25', priority: 'high' },
    { id: 2, title: 'Send thank you letter to new donors', dueDate: '2023-10-26', priority: 'medium' },
    { id: 3, title: 'Prepare presentation for board meeting', dueDate: '2023-10-27', priority: 'high' },
    { id: 4, title: 'Update donor database with new contacts', dueDate: '2023-10-28', priority: 'medium' },
    { id: 5, title: 'Review event budget for Fall Gala', dueDate: '2023-10-29', priority: 'high' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800">Tasks Due This Week</h2>
        <button className="text-sm px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700">+ New Task</button>
      </div>
      
      <div className="space-y-2">
        {tasks.map((task) => (
          <div key={task.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
            <input type="checkbox" className="h-4 w-4 text-blue-600 rounded mr-3" />
            <div className="flex-grow">
              <p className="text-sm font-medium text-gray-800">{task.title}</p>
              <p className="text-xs text-gray-500">Due: {task.dueDate}</p>
            </div>
            <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${
              task.priority === 'high' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
            }`}>
              {task.priority === 'high' ? 'High' : 'Medium'}
            </span>
          </div>
        ))}
      </div>
      
      <div className="mt-4 text-center">
        <button className="text-sm text-blue-600 hover:underline">View All Tasks</button>
      </div>
    </div>
  );
};

export default TaskList;
